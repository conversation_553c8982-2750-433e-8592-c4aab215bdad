import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "v3.fal.media",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "storage.rapidmock.dev",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "pic.rapidmock.dev",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "img.imagefox.art",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "replicate.delivery",
        port: "",
        pathname: "/**",
      },
    ],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // 性能优化
  compress: true,
  poweredByHeader: false,

  // SEO优化
  trailingSlash: false,

  // 安全头部
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ]
  },

  // 重定向配置
  async redirects() {
    return [
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/image-generator',
        destination: '/free-image-generator',
        permanent: true,
      },
    ]
  },

  experimental: {
    // ... existing code ...
  }
};

export default nextConfig;
