'use client'
import React, { useState } from 'react';
import {
  Mail,
  Phone,
  MapPin,
  Clock,
  Send,
  MessageCircle,
  Users,
  Headphones,
  Globe,
  Shield,
  Zap,
  CheckCircle
} from 'lucide-react';
import { Metadata } from 'next';
import { Turnstile } from '@marsidev/react-turnstile';
import { toast } from 'sonner';

// Note: Since this is a client component, metadata should be defined in a parent server component or layout
// For now, we'll add the metadata in the layout file or create a separate server component wrapper

export default function ContactUs() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    subject: '',
    category: '',
    message: '',
    priority: 'medium'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [token, setToken] = useState<string>('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!token) {
      toast.error('请完成人机验证');
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          token,
        }),
      });

      if (!response.ok) {
        throw new Error('提交失败');
      }

      toast.success('留言提交成功');
      // 重置表单
      setFormData({
        name: '',
        email: '',
        company: '',
        subject: '',
        category: '',
        message: '',
        priority: 'medium'
      });
      setToken('');
    } catch (error) {
      toast.error('提交失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: <Mail className="w-6 h-6" />,
      title: "Email (please replace # with @)",
      content: "hi#imagefox.art",
      description: "We typically reply within 24 hours",
      color: "blue" // used for styling, not translation
    }
    // Add other contact methods like Phone or Address if needed
    // {
    //   icon: <Phone className="w-6 h-6" />,
    //   title: "Phone",
    //   content: "+****************",
    //   description: "Available during office hours",
    //   color: "green"
    // },
    // {
    //   icon: <MapPin className="w-6 h-6" />,
    //   title: "Office Address",
    //   content: "123 AI Street, Innovation City, CA 94000",
    //   description: "Visits by appointment only",
    //   color: "purple"
    // }
  ];

  const supportCategories = [
    {
      icon: <Headphones className="w-8 h-8" />,
      title: "Technical Support",
      description: "Feature usage, troubleshooting",
      color: "bg-blue-500"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Sales Inquiry",
      description: "Product pricing, enterprise solutions, partnership discussions",
      color: "bg-green-500"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Account Security",
      description: "Account issues, security settings, permission management",
      color: "bg-red-500"
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Product Feedback",
      description: "Feature suggestions, user experience, product improvements",
      color: "bg-purple-500"
    }
  ];

  const officeHours = [
    { day: "Monday - Friday", time: "9:00 AM - 6:00 PM PST" },
    { day: "Saturday", time: "10:00 AM - 4:00 PM PST" },
    { day: "Sunday", time: "Closed" }
  ];

  return (
    <div className="min-h-screen from-slate-50 via-blue-50 to-indigo-100">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 opacity-10"></div>
        <div className="relative max-w-7xl mx-auto px-6 py-10">
          <div className="text-center">
            <h1 className="text-5xl font-bold text- mb-6">
              Contact ImageFox - AI Image Generator Support
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Need help with our AI image generator? Our expert support team is ready to assist you with technical questions, AI image generation guidance, account issues, or business collaboration opportunities. Get professional support for all your AI art creation needs.
            </p>
            <div className="mt-8 flex justify-center space-x-4">
              <div className="flex items-center text-gray-600">
                <Globe className="w-5 h-5 mr-2" />
                <span>Global Service</span>
              </div>
              <div className="flex items-center text-gray-600">
                <Clock className="w-5 h-5 mr-2" />
                <span>Fast Response</span>
              </div>
              <div className="flex items-center text-gray-600">
                <Shield className="w-5 h-5 mr-2" />
                <span>Professional Support</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-16">
        <div className="grid lg:grid-cols-3 gap-12">
          
          {/* Contact Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
              <div className="bg-gradient-to-r from-gray-400 to-gray-600 p-8">
                <h2 className="text-3xl font-bold text-white mb-2">Send Us a Message</h2>
                <p className="text-gray-300 mb-2">Please fill out the form below, and we will get back to you as soon as possible.</p>
              </div>
              
              <div className="p-8">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-2">
                        Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        placeholder="Enter your name"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="company" className="block text-sm font-semibold text-gray-700 mb-2">
                        Company Name
                      </label>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        placeholder="Your company name (optional)"
                      />
                    </div>
                    <div>
                      <label htmlFor="category" className="block text-sm font-semibold text-gray-700 mb-2">
                        Inquiry Category *
                      </label>
                      <select
                        id="category"
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      >
                        <option value="">Please select an inquiry category</option>
                        <option value="technical">Technical Support</option>
                        <option value="sales">Sales Inquiry</option>
                        <option value="security">Account Security</option>
                        <option value="feedback">Product Feedback</option>
                        <option value="partnership">Business Partnership</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-semibold text-gray-700 mb-2">
                      Subject *
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      placeholder="Briefly describe your issue or requirement"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Priority
                    </label>
                    <div className="flex space-x-4">
                      {['low', 'medium', 'high'].map((priority) => (
                        <label key={priority} className="flex items-center">
                          <input
                            type="radio"
                            name="priority"
                            value={priority}
                            checked={formData.priority === priority}
                            onChange={handleInputChange}
                            className="mr-2 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-700 capitalize">
                            {priority}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-semibold text-gray-700 mb-2">
                      Detailed Description *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                      placeholder="Please describe your problem, needs, or suggestions in detail..."
                    />
                  </div>

                  {/* 添加 Turnstile 验证 */}
                  <div className="flex justify-center">
                    <Turnstile
                      siteKey={'0x4AAAAAABPToBobzrFbjtye'}
                      onSuccess={setToken}
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-to-r from-gray-600 to-gray-800 text-white py-4 px-6 rounded-lg font-semibold hover:from-gray-700 hover:to-gray-900 transform hover:scale-[1.02] transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Send className="w-5 h-5" />
                    <span>{isSubmitting ? '提交中...' : 'Send Message'}</span>
                  </button>
                </form>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-8">
            
            {/* Contact Methods */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Contact Methods</h3>
              <div className="space-y-6">
                {contactInfo.map((item, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className={`p-3 rounded-lg bg-${item.color}-100 text-${item.color}-600`}>
                      {item.icon}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{item.title}</h4>
                      <p className="text-gray-700 font-medium">{item.content}</p>
                      <p className="text-sm text-gray-500">{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Office Hours */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
              <div className="flex items-center space-x-3 mb-6">
                <Clock className="w-6 h-6 text-blue-600" />
                <h3 className="text-2xl font-bold text-gray-900">Office Hours</h3>
              </div>
              <div className="space-y-3">
                {officeHours.map((schedule, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                    <span className="font-medium text-gray-700">{schedule.day}</span>
                    <span className="text-gray-600">{schedule.time}</span>
                  </div>
                ))}
              </div>
              <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
                <p className="text-sm text-green-700">
                  <strong>Emergency Support:</strong> Paid users receive 24/7 priority technical support.
                </p>
              </div>
            </div>
            
          </div>
        </div>

        {/* Support Categories */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">How Can We Help You?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our professional team covers multiple areas to provide you with comprehensive support services.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {supportCategories.map((category, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-shadow duration-300">
                <div className={`w-16 h-16 ${category.color} rounded-xl flex items-center justify-center text-white mb-4 mx-auto`}>
                  {category.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 text-center mb-3">
                  {category.title}
                </h3>
                <p className="text-gray-600 text-center text-sm leading-relaxed">
                  {category.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Preview */}
        <div className="mt-20 bg-white rounded-2xl shadow-xl border border-gray-100 p-12">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-gray-600">Check out our most common questions and answers.</p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold text-gray-900 mb-2">What image formats are supported?</h4>
                <p className="text-gray-600 text-sm">We support major formats like PNG, JPEG, WebP, with resolutions up to 4K.</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold text-gray-900 mb-2">How can I upgrade my plan?</h4>
                <p className="text-gray-600 text-sm">You can upgrade your plan anytime in your account settings, and the upgrade takes effect immediately.</p>
              </div>
            </div>
            <div className="space-y-6">
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-semibold text-gray-900 mb-2">How is data security ensured?</h4>
                <p className="text-gray-600 text-sm">We use end-to-end encryption and comply with SOC2 and GDPR standards to ensure your data security.</p>
              </div>
              <div className="border-l-4 border-orange-500 pl-4">
                <h4 className="font-semibold text-gray-900 mb-2">Is there a free trial available?</h4>
                <p className="text-gray-600 text-sm">Yes, we offer a 7-day free trial for you to explore all our premium features. No credit card required.</p>
              </div>
            </div>
          </div>
          
          <div className="text-center mt-8">
            <a href="/faq" className="inline-flex items-center space-x-2 text-blue-600 hover:text-blue-700 font-medium">
              <span>View More FAQs</span>
              <span>→</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}