import { Metadata } from 'next';
import { ServiceStructuredData, ProductStructuredData, HowToStructuredData } from "@/components/seo-optimizer";
import LandingPageClient from "@/components/landing-page-client";

export const metadata: Metadata = {
  title: "ImageFox - Free AI Image Generator | Create Stunning AI Art from Text",
  description: "Generate stunning AI images from text descriptions for free with ImageFox. Our advanced AI image generator creates professional-quality artwork, logos, and visuals in seconds. Transform your ideas into beautiful AI art with our free text-to-image AI tool.",
  keywords: "free AI image generator, AI art generator, text to image AI, create images from text, AI artwork generator, artificial intelligence art, AI image creation, free AI art tool, text to art generator, AI visual creator, digital art AI, AI graphics generator, machine learning art, neural network images, AI design tool, automated image generation, AI picture maker, intelligent art creation, generative AI art, AI powered image creation",
  alternates: {
    canonical: 'https://imagefox.art/',
  },
  openGraph: {
    title: "ImageFox - Free AI Image Generator | Create Stunning AI Art from Text",
    description: "Generate stunning AI images from text descriptions for free with ImageFox. Transform your ideas into beautiful AI art with our advanced text-to-image AI generator.",
    url: 'https://imagefox.art/',
    images: [
      {
        url: '/logo.png',
        width: 1200,
        height: 630,
        alt: 'ImageFox Free AI Image Generator - Create AI Art from Text',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "ImageFox - Free AI Image Generator | Create Stunning AI Art from Text",
    description: "Generate stunning AI images from text descriptions for free with ImageFox. Transform your ideas into beautiful AI art instantly.",
    images: ['/logo.png'],
  },
};

// 服务器组件 - 默认导出
export default function LandingPage() {
  return (
    <>
      <LandingPageClient />
      {/* 结构化数据 - 在服务器端渲染 */}
      <ServiceStructuredData
        name="AI Image Generator Service"
        description="Professional AI image generation service that transforms text descriptions into stunning visual art. Create high-quality images, artwork, and graphics using advanced artificial intelligence technology."
        serviceType="AI Image Generation and Photo Editing"
      />

      <ProductStructuredData
        name="ImageFox AI Image Generator"
        description="Free AI image generator that creates stunning images from text descriptions. Advanced AI technology for professional-quality image generation."
        image="https://imagefox.art/logo.png"
        url="https://imagefox.art/free-image-generator"
        price="0"
      />

      <HowToStructuredData
        name="How to Generate AI Images with ImageFox"
        description="Learn how to create stunning AI images from text descriptions using ImageFox's free AI image generator."
        image="https://imagefox.art/logo.png"
        totalTime="PT2M"
        steps={[
          {
            name: "Visit ImageFox AI Image Generator",
            text: "Go to ImageFox.art and navigate to the free AI image generator tool."
          },
          {
            name: "Enter Your Text Description",
            text: "Type a detailed description of the image you want to create in the text prompt box."
          },
          {
            name: "Select AI Model and Settings",
            text: "Choose your preferred AI model and adjust settings like image size and style."
          },
          {
            name: "Generate Your AI Image",
            text: "Click the generate button and wait for the AI to create your custom image."
          },
          {
            name: "Download Your AI Artwork",
            text: "Once generated, download your high-quality AI image for personal or commercial use."
          }
        ]}
      />
    </>
  );
}


