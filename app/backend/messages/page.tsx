import { prisma } from '@/lib/prisma'
import { DataTable } from '@/components/ui/data-table'
import { columns } from './columns'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import Link from 'next/link'
import { auth } from '@/auth'
import { redirect } from 'next/navigation'

export default async function MessagesPage() {
  const session = await auth()
  if (!session?.user?.email) {
    redirect('/login')
  }
  if(session.user.email !== '<EMAIL>') {
    redirect('/')
  }
  const messages = await prisma.message.findMany({
    orderBy: {
      createdAt: 'desc',
    },
  })

  return (
    <div className="container mx-auto py-10 min-h-screen">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold">留言管理</h1>
      </div>
      <DataTable columns={columns} data={messages} />
    </div>
  )
} 