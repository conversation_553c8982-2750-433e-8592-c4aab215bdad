'use client'

import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { formatDate } from '@/lib/utils'

export type Message = {
  id: string
  name: string
  email: string
  company: string | null
  subject: string
  category: string
  message: string
  priority: string
  isRead: boolean
  createdAt: Date
}

export const columns: ColumnDef<Message>[] = [
  { accessorKey: 'name', header: '姓名' },
  { accessorKey: 'email', header: '邮箱' },
  { accessorKey: 'company', header: '公司' },
  { accessorKey: 'subject', header: '主题' },
  { accessorKey: 'category', header: '分类' },
  { accessorKey: 'priority', header: '优先级' },
  {
    accessorKey: 'message',
    header: '内容',
    cell: ({ row }) => {
      const content = row.getValue('message') as string
      return (
        <div className="max-w-md truncate">{content}</div>
      )
    },
  },
  {
    accessorKey: 'isRead',
    header: '状态',
    cell: ({ row }) => {
      const isRead = row.getValue('isRead') as boolean
      return (
        <Badge variant={isRead ? 'default' : 'secondary'}>
          {isRead ? '已读' : '未读'}
        </Badge>
      )
    },
  },
  {
    accessorKey: 'createdAt',
    header: '提交时间',
    cell: ({ row }) => formatDate(row.getValue('createdAt')),
  },
] 