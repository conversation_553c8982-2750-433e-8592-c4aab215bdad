import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Header from "../components/header";
import Footer from "../components/footer";
import { Toaster } from "../components/ui/sonner";
import { SessionProvider } from "next-auth/react";
import CookieConsent from "../components/cookie-consent";
import { auth as nextAuth } from "@/auth";
import { GoogleAnalytics } from "@next/third-parties/google"
import StructuredData, { OrganizationStructuredData, SoftwareApplicationStructuredData } from "../components/structured-data";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ImageFox - Free AI Image Generator & Photo Editor Online | Create Stunning AI Art",
  description: "Create stunning AI images from text with ImageFox's free AI image generator and photo editor. Transform text to images, remove backgrounds, enhance photos with AI. Generate professional AI art, logos, and visuals instantly. No design skills required - start creating amazing AI artwork today!",
  keywords: "AI image generator, free AI image generator, text to image AI, AI photo editor, artificial intelligence art, AI art generator, create images from text, photo editing online, AI visual content, background removal AI, AI image enhancement, free image creation tool, AI artwork generator, text to art AI, AI design tool, online AI editor, AI graphics generator, digital art AI, AI image maker, free AI art generator, AI picture creator, automated image generation, AI visual design, machine learning image creation, neural network art, AI powered photo editor, intelligent image processing, AI creative tools, generative AI art",
  authors: [{ name: "ImageFox Team" }],
  creator: "ImageFox",
  publisher: "ImageFox",
  category: "Technology",
  classification: "AI Image Generation Software",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://imagefox.art'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "ImageFox - Free AI Image Generator & Photo Editor Online | Create Stunning AI Art",
    description: "Create stunning AI images from text with ImageFox's free AI image generator and photo editor. Transform text to images, remove backgrounds, enhance photos with AI. Generate professional AI art instantly!",
    url: 'https://imagefox.art',
    siteName: 'ImageFox - AI Image Generator',
    images: [
      {
        url: '/logo.png',
        width: 1200,
        height: 630,
        alt: 'ImageFox - Free AI Image Generator and Photo Editor for Creating Stunning AI Art',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "ImageFox - Free AI Image Generator & Photo Editor Online | Create Stunning AI Art",
    description: "Create stunning AI images from text with ImageFox's free AI image generator and photo editor. Transform text to images, remove backgrounds, enhance photos with AI.",
    images: ['/logo.png'],
    creator: '@imagefox',
    site: '@imagefox',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    //google: 'your-google-verification-code',
    //yandex: 'your-yandex-verification-code',
    //yahoo: 'your-yahoo-verification-code',
    //bing: 'your-bing-verification-code',
  },
  other: {
    'theme-color': '#ffffff',
    'color-scheme': 'light',
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
  },
};

export default async function RootLayout({
  children,
  auth,
}: Readonly<{
  children: React.ReactNode;
  auth: React.ReactNode;
}>) {
  const session = await nextAuth()
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SessionProvider session={session} refetchInterval={60}> {/* 添加 refetchInterval={5} (每5秒刷新一次) */}
        <div className="w-full bg-white text-black font-['Inter']">
          <Header />
          {children}
          <Footer />
        </div>
        
        {auth}
        </SessionProvider>
        <CookieConsent
        companyName="ImageFox"
        privacyPolicyUrl="/privacy-policy"
      />
        <GoogleAnalytics gaId="G-ZBXXHST9LV" />
        <StructuredData type="webapp" />
        <OrganizationStructuredData />
        <SoftwareApplicationStructuredData />
        <Toaster position="top-center" />
      </body>
    </html>
  );
}
