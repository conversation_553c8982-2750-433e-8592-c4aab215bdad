'use client';
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { redirect } from "next/navigation";
import { updateName } from "./action";
import { useActionState, useEffect } from "react";
import { toast } from "sonner";

export default function UserInfo({ user }: { user: any }) {
    const { data: session } = useSession();
    if (!session?.user) {
        redirect("/sign-in");
    }
    const [state, formAction, isPending] = useActionState(updateName, user, undefined);

     const formattedJoinDate = new Date(user.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }); 
    useEffect(() => {
        if (state.success) {
            // Optionally, you can show a success message or perform any other action
            toast.success(state.success);
        }
        if (state.error) {
            // Optionally, you can show an error message or perform any other action
            toast.error(state.error);
        }
    }, [state]);
    return (
        <div className="p-5 border border-gray-200">
            <h3 className="text-lg font-semibold mb-4">Profile Information</h3>
            <form action={formAction}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                        id="name"
                        name="name"
                        defaultValue={state.name || ""}
                        placeholder="Your full name"
                        className="mt-1"
                        required
                    />
                </div>
                <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                        id="email"
                        type="email"
                        defaultValue={state.email ?? ""}
                        readOnly
                        className="mt-1 bg-gray-50"
                    />
                    <p className="text-xs text-gray-400 mt-1">Email cannot be changed</p>
                </div>
                <div>
                    <Label htmlFor="joinDate">Join Date</Label>
                    <Input
                        id="joinDate"
                        defaultValue={formattedJoinDate}
                        readOnly
                        className="mt-1 bg-gray-50"
                    />
                </div>
            </div>
            <div className="flex justify-end mt-6">
                <Button className="cursor-pointer" type="submit">{isPending ? "Saving..." : "Save Changes"}</Button>
            </div>
        </form>
    </div>
    )
}