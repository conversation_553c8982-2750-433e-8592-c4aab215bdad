'use client'

import { <PERSON><PERSON> } from "./ui/button"
import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ertTriangle, CheckCircle, Lightbulb, Users, Briefcase, PenTool, ShoppingCart, BookOpen, BrainCircuit, ImagePlay, Settings2, Rocket } from "lucide-react"
import Link from "next/link"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import FAQStructuredData, { imageFoxFAQs } from "@/components/faq-structured-data"
import Image from 'next/image';

export default function LandingPageClient() {
  const caseStudyImages = [
    'https://img.imagefox.art/3.webp',
    'https://img.imagefox.art/4.webp',
    'https://img.imagefox.art/3.webp',
    'https://img.imagefox.art/3.webp',
    'https://img.imagefox.art/3.webp',
    'https://img.imagefox.art/4.webp',
    'https://img.imagefox.art/3.webp',
    'https://img.imagefox.art/3.webp',
  ]
  
  return (
    <>
    {/* Hero Section */}
    <section className="relative pt-16 min-h-screen flex overflow-hidden hero-background-pattern">
        <div className="absolute inset-0 w-full h-full"> 
          <svg viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="xMidYMid slice">
    
    <defs>
      
      <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{stopColor: '#f8fafc', stopOpacity: 1}} />
        <stop offset="50%" style={{stopColor: '#f1f5f9', stopOpacity: 1}} />
        <stop offset="100%" style={{stopColor: '#e2e8f0', stopOpacity: 1}} />
      </linearGradient>
      
      
      <radialGradient id="accentGradient" cx="30%" cy="30%">
        <stop offset="0%" style={{stopColor: '#3b82f6', stopOpacity: 0.1}} />
        <stop offset="100%" style={{stopColor: '#1e40af', stopOpacity: 0.05}} />
      </radialGradient>
      
      <radialGradient id="accentGradient2" cx="70%" cy="70%">
        <stop offset="0%" style={{stopColor: '#8b5cf6', stopOpacity: 0.08}} />
        <stop offset="100%" style={{stopColor: '#5b21b6', stopOpacity: 0.03}} />
      </radialGradient>
      
      
      <filter id="glow">
        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
        <feMerge> 
          <feMergeNode in="coloredBlur"/>
          <feMergeNode in="SourceGraphic"/>
        </feMerge>
      </filter>
      
      
      <pattern id="dots" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
        <circle cx="20" cy="20" r="1.5" fill="#94a3b8" opacity="0.3"/>
      </pattern>
    </defs>
    
    <rect width="1200" height="600" fill="url(#mainGradient)"/>
    
    <rect width="1200" height="600" fill="url(#accentGradient)"/>
    <rect width="1200" height="600" fill="url(#accentGradient2)"/>
    
      
    <rect width="1200" height="600" fill="url(#dots)" opacity="0.4"/>
    

    <circle cx="150" cy="100" r="80" fill="#3b82f6" opacity="0.05">
      <animate attributeName="r" values="80;100;80" dur="8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.05;0.08;0.05" dur="8s" repeatCount="indefinite"/>
    </circle>
    

    <circle cx="1000" cy="150" r="60" fill="#8b5cf6" opacity="0.06">
      <animate attributeName="r" values="60;80;60" dur="10s" repeatCount="indefinite"/>
    </circle>
    

    <g opacity="0.08">
  
      <polygon points="300,80 320,95 320,125 300,140 280,125 280,95" fill="#1e40af">
        <animateTransform attributeName="transform" type="rotate" values="0 300 110;360 300 110" dur="20s" repeatCount="indefinite"/>
      </polygon>
      

      <polygon points="800,400 820,430 780,430" fill="#5b21b6">
        <animateTransform attributeName="transform" type="rotate" values="0 800 420;-360 800 420" dur="25s" repeatCount="indefinite"/>
      </polygon>
      
    
      <polygon points="950,300 970,320 950,340 930,320" fill="#0f172a">
        <animate attributeName="opacity" values="0.08;0.15;0.08" dur="6s" repeatCount="indefinite"/>
      </polygon>
    </g>
    

    <g stroke="#64748b" strokeWidth="1" fill="none" opacity="0.2">
      <path d="M 0,300 Q 300,250 600,300 T 1200,280">
        <animate attributeName="d" values="M 0,300 Q 300,250 600,300 T 1200,280;M 0,320 Q 300,270 600,320 T 1200,300;M 0,300 Q 300,250 600,300 T 1200,280" dur="12s" repeatCount="indefinite"/>
      </path>
      <path d="M 0,350 Q 400,300 800,350 T 1200,330">
        <animate attributeName="d" values="M 0,350 Q 400,300 800,350 T 1200,330;M 0,370 Q 400,320 800,370 T 1200,350;M 0,350 Q 400,300 800,350 T 1200,330" dur="15s" repeatCount="indefinite"/>
      </path>
    </g>

    <g stroke="#cbd5e1" strokeWidth="0.5" opacity="0.1">
      <line x1="200" y1="0" x2="200" y2="600"/>
      <line x1="400" y1="0" x2="400" y2="600"/>
      <line x1="600" y1="0" x2="600" y2="600"/>
      <line x1="800" y1="0" x2="800" y2="600"/>
      <line x1="1000" y1="0" x2="1000" y2="600"/>
      
      <line x1="0" y1="150" x2="1200" y2="150"/>
      <line x1="0" y1="300" x2="1200" y2="300"/>
      <line x1="0" y1="450" x2="1200" y2="450"/>
    </g>
    
  
    <g>
      <circle cx="500" cy="200" r="2" fill="#3b82f6" filter="url(#glow)">
        <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite" begin="0s"/>
      </circle>
      <circle cx="700" cy="250" r="1.5" fill="#8b5cf6" filter="url(#glow)">
        <animate attributeName="opacity" values="0;1;0" dur="5s" repeatCount="indefinite" begin="2s"/>
      </circle>
      <circle cx="350" cy="400" r="2.5" fill="#1e40af" filter="url(#glow)">
        <animate attributeName="opacity" values="0;1;0" dur="6s" repeatCount="indefinite" begin="1s"/>
      </circle>
    </g>
    

    <path d="M 0,500 Q 300,480 600,500 T 1200,490 L 1200,600 L 0,600 Z" fill="#f1f5f9" opacity="0.5"/>
    

    <path d="M 0,0 L 1200,0 L 1200,120 Q 900,100 600,120 T 0,100 Z" fill="#ffffff" opacity="0.3"/>
    
          </svg>
        </div>
        {/* Hero Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">Free AI Image Generator: Create Stunning AI Art from Text Instantly</h1>
            <h2 className="text-2xl md:text-3xl font-semibold mb-6 leading-tight text-[#555555]">Transform Text to Images with Advanced AI Technology</h2>
            <p className="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed text-[#333333]">
              Generate professional-quality AI images from simple text descriptions in seconds. Our free AI image generator uses cutting-edge artificial intelligence to transform your ideas into stunning visual art. No design experience needed - just describe what you want and watch our AI create beautiful images instantly.
            </p>
            <div className="space-y-4">
              <Link href="/free-image-generator">
                <Button
                  size="lg"
                  className="cursor-pointer bg-[#121212] text-white hover:bg-[#333333] hover:scale-105 transition-all duration-300 text-lg px-8 py-4"
                >
                  Start Creating AI Art Free
                </Button>
              </Link>
              <p className="mt-8 text-sm text-[#666666]">100% Free AI Image Generator · No Credit Card Required · Instant Results</p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Value Proposition */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Choose Our AI Image Generator?</h2>
            <p className="text-lg text-[#666666] max-w-3xl mx-auto">
              Experience the power of advanced AI technology that transforms text into stunning visual art
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center group"
            >
              <div className="w-12 h-12 mx-auto mb-4 group-hover:scale-105 transition-transform duration-300">
                <Cpu size={48} strokeWidth={2} />
              </div>
              <h3 className="text-2xl font-semibold mb-4">Lightning-Fast AI Image Generation</h3>
              <p className="text-[#666666] leading-relaxed mb-2">Generate high-quality AI images in under 15 seconds</p>
              <p className="text-[#666666] leading-relaxed">Batch AI image processing for multiple creations</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center group"
            >
              <div className="w-12 h-12 mx-auto mb-4 group-hover:scale-105 transition-transform duration-300">
                <Palette size={48} strokeWidth={2} />
              </div>
              <h3 className="text-2xl font-semibold mb-4">Precise AI Art Control</h3>
              <p className="text-[#666666] leading-relaxed mb-2">Advanced text-to-image AI with style combinations</p>
              <p className="text-[#666666] leading-relaxed">Mix artistic styles like "Monet + Steampunk" effortlessly</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center group"
            >
              <div className="w-12 h-12 mx-auto mb-4 group-hover:scale-105 transition-transform duration-300">
                <Layers size={48} strokeWidth={2} />
              </div>
              <h3 className="text-2xl font-semibold mb-4">Professional AI Image Quality</h3>
              <p className="text-[#666666] leading-relaxed mb-2">• Ultra-high 4096×4096 resolution AI images</p>
              <p className="text-[#666666] leading-relaxed">• Full commercial license for AI-generated artwork</p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* 它解决什么问题 Section */}
      <section className="py-20 bg-[#F5F5F5]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Struggling with Image Creation? Our AI Image Generator Solves Everything</h2>
            <p className="text-lg md:text-xl text-[#666666] max-w-3xl mx-auto">
              ImageFox's advanced AI image generator eliminates creative barriers, empowering everyone to create professional-quality AI art and stunning visual content from simple text descriptions.
            </p>
          </motion.div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <Lightbulb size={40} strokeWidth={1.5} className="text-yellow-500" />,
                title: "Creative Block? Need AI Art Inspiration?",
                description: "Our AI image generator breaks creative barriers, transforming simple text prompts into unlimited visual possibilities. Generate unique AI art that sparks new ideas and creative directions.",
              },
              {
                icon: <AlertTriangle size={40} strokeWidth={1.5} className="text-red-500" />,
                title: "No Design Skills? No Problem with AI!",
                description: "Skip expensive design courses. Our text-to-image AI generator requires zero design experience. Simply describe your vision and watch our AI create professional-quality images instantly.",
              },
              {
                icon: <ShoppingCart size={40} strokeWidth={1.5} className="text-green-500" />,
                title: "Expensive Stock Photos? Generate AI Images Free!",
                description: "Eliminate costly stock photo subscriptions. Create unlimited original AI-generated images for commercial use. Our free AI image generator provides unique visuals without licensing fees.",
              },
              {
                icon: <CheckCircle size={40} strokeWidth={1.5} className="text-blue-500" />,
                title: "Complex Design Software? Use Simple AI Instead!",
                description: "Forget complicated design tools. Our intuitive AI image generator creates stunning visuals from text in seconds. Focus on your creative vision while AI handles the technical execution.",
              },
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-lg text-center group"
              >
                <div className="w-12 h-12 mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  {item.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3 text-[#333333]">{item.title}</h3>
                <p className="text-[#666666] leading-relaxed">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* 它有什么功能 Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Advanced AI Image Generator Features for Every Creator</h2>
            <p className="text-lg md:text-xl text-[#666666] max-w-3xl mx-auto">
              Discover our comprehensive suite of AI-powered image generation tools designed to transform text into stunning visual art with professional-grade quality and unlimited creative possibilities.
            </p>
          </motion.div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <BrainCircuit size={48} strokeWidth={1.5} />,
                title: "Advanced Text-to-Image AI Generator",
                description: "Transform any text description into stunning AI-generated images. Our intelligent AI image generator understands complex prompts and creates highly detailed, contextually accurate visual art that perfectly matches your vision.",
              },
              {
                icon: <ImagePlay size={48} strokeWidth={1.5} />,
                title: "Multiple AI Art Styles & Genres",
                description: "Generate AI images in countless artistic styles - from photorealistic portraits to abstract art, oil paintings, watercolors, cyberpunk, anime, and more. Blend multiple styles to create unique AI artwork that stands out.",
              },
              {
                icon: <Settings2 size={48} strokeWidth={1.5} />,
                title: "AI-Powered Photo Editing Suite",
                description: "Enhance your AI-generated images with smart editing tools. Remove backgrounds, optimize quality, resize images, and apply AI-driven enhancements to create professional-grade visual content effortlessly.",
              },
              {
                icon: <Rocket size={48} strokeWidth={1.5} />,
                title: "High-Resolution AI Image Output",
                description: "Generate ultra-high-definition AI images up to 4096x4096 resolution. Batch process multiple AI image generations simultaneously for commercial projects, marketing materials, and professional applications.",
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="p-6 rounded-lg text-center group border border-gray-200 hover:shadow-xl transition-shadow duration-300"
              >
                <div className="w-16 h-16 bg-[#121212] text-white rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-[#333333] group-hover:scale-105 transition-all duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3 text-[#333333]">{feature.title}</h3>
                <p className="text-[#666666] leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* 哪些人在使用它 Section */}
      <section className="py-20 bg-[#F5F5F5]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Professionals Worldwide Trust Our AI Image Generator</h2>
            <p className="text-lg text-[#666666] max-w-3xl mx-auto">
              From marketing experts to content creators, thousands of professionals use our AI image generator to create stunning visual content and boost their creative productivity.
            </p>
          </motion.div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { icon: <Users size={40} strokeWidth={1.5} />, name: "Marketing Professionals", description: "Generate compelling AI images for advertising campaigns, social media content, and marketing materials. Create eye-catching visuals that boost engagement and conversion rates with our AI image generator." },
              { icon: <PenTool size={40} strokeWidth={1.5} />, name: "Content Creators & Bloggers", description: "Enhance blogs, videos, and digital content with unique AI-generated images. Create original visual content that captivates audiences and improves content performance across all platforms." },
              { icon: <Briefcase size={40} strokeWidth={1.5} />, name: "Small Business Owners", description: "Build professional brand identity with custom AI-generated logos, product images, and marketing visuals. Cost-effective solution for creating high-quality business graphics without expensive design services." },
              { icon: <Palette size={40} strokeWidth={1.5} />, name: "Designers & Artists", description: "Accelerate creative workflows with AI-generated concept art, mood boards, and design inspiration. Use our AI image generator to explore new artistic directions and expand creative possibilities." },
              { icon: <BookOpen size={40} strokeWidth={1.5} />, name: "Educators & Students", description: "Create engaging educational materials with custom AI-generated illustrations, diagrams, and visual aids. Enhance learning experiences with relevant, high-quality images for presentations and coursework." },
              { icon: <Cpu size={40} strokeWidth={1.5} />, name: "Developers & Product Managers", description: "Generate product mockups, app screenshots, UI illustrations, and technical documentation visuals. Streamline development workflows with AI-created graphics for prototypes and presentations." },
            ].map((user, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white p-8 rounded-xl shadow-lg group hover:shadow-2xl transition-shadow duration-300"
              >
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mr-4 text-[#121212] group-hover:bg-[#121212] group-hover:text-white transition-colors duration-300">
                    {user.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-[#333333]">{user.name}</h3>
                </div>
                <p className="text-[#666666] leading-relaxed">{user.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Real-time Demo */}
      <section className="py-20 bg-[#F5F5F5]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Case Study</h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
            {caseStudyImages.map((src, index) => {
              const height = 200 + (index % 3) * 50
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="col-span-2 md:col-span-1"
                  style={{ height }}
                >
                  <Image
                    src={src}
                    alt={`Professional AI generated artwork example ${index + 1} created with ImageFox text-to-image AI generator - showcasing high-quality AI art and visual content creation capabilities`}
                    width={500}
                    height={height}
                    className="w-full h-full object-cover rounded-lg"
                    loading={index < 4 ? "eager" : "lazy"}
                    placeholder="blur"
                    blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
                  />
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Subscription Coming Soon...</h2>
          </motion.div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">FAQ</h2>
          </motion.div>

          <Accordion type="single" collapsible className="space-y-4">
          <AccordionItem value="item-1" className="border border-[#E0E0E0] rounded-lg px-6">
            <AccordionTrigger className="text-left text-[#333333] font-semibold text-lg">Who owns the copyright to generated images?</AccordionTrigger>
            <AccordionContent>
              Users have full commercial usage rights to the generated images, which can be used for commercial projects, product designs, and marketing materials.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-2" className="border border-[#E0E0E0] rounded-lg px-6">
            <AccordionTrigger className="text-left text-[#333333] font-semibold text-lg">Do you support batch generation via API?</AccordionTrigger>
            <AccordionContent>
              API calls are currently not supported.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-3" className="border border-[#E0E0E0] rounded-lg px-6">
            <AccordionTrigger className="text-left text-[#333333] font-semibold text-lg">What is the maximum supported resolution?</AccordionTrigger>
            <AccordionContent>
              Basic plan supports up to 1024px, Pro plan supports up to 2048px, and Enterprise plan supports ultra-HD output up to 4096px.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-4" className="border border-[#E0E0E0] rounded-lg px-6">
            <AccordionTrigger className="text-left text-[#333333] font-semibold text-lg">Do you support custom model training?</AccordionTrigger>
            <AccordionContent>
              Custom model training is currently not supported.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-5" className="border border-[#E0E0E0] rounded-lg px-6">
            <AccordionTrigger className="text-left text-[#333333] font-semibold text-lg">What factors affect generation speed?</AccordionTrigger>
            <AccordionContent>
              Image complexity, resolution, and current system load. Average generation time: &lt;15 seconds for 512px, &lt;30 seconds for 1024px.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-6" className="border border-[#E0E0E0] rounded-lg px-6">
            <AccordionTrigger className="text-left text-[#333333] font-semibold text-lg">Are there any limitations on the free plan?</AccordionTrigger>
            <AccordionContent>
              20 basic resolution images per month, watermark-free and available for commercial use.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-7" className="border border-[#E0E0E0] rounded-lg px-6">
            <AccordionTrigger className="text-left text-[#333333] font-semibold text-lg">What happens to unused credits when upgrading a subscription?</AccordionTrigger>
            <AccordionContent>
              Free plan credits will be reset to zero. For paid plans, unused credits will be prorated and carried over to the new billing cycle.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-8" className="border border-[#E0E0E0] rounded-lg px-6">
            <AccordionTrigger className="text-left text-[#333333] font-semibold text-lg">What payment methods do you accept?</AccordionTrigger>
            <AccordionContent>
              Credit cards (Visa/Mastercard/Amex) and PayPal.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-9" className="border border-[#E0E0E0] rounded-lg px-6">
            <AccordionTrigger className="text-left text-[#333333] font-semibold text-lg">Can I cancel my subscription? What's the refund policy?</AccordionTrigger>
            <AccordionContent>
              You can cancel anytime, and you'll receive a prorated refund for unused months.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-10" className="border border-[#E0E0E0] rounded-lg px-6">
            <AccordionTrigger className="text-left text-[#333333] font-semibold text-lg">Do you support image editing/expansion?</AccordionTrigger>
            <AccordionContent>
              Image editing/expansion is currently not supported.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-11" className="border border-[#E0E0E0] rounded-lg px-6">
            <AccordionTrigger className="text-left text-[#333333] font-semibold text-lg">Can it be integrated into design workflows?</AccordionTrigger>
            <AccordionContent>
              Currently not supported.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        </div>
      </section>

      {/* FAQ结构化数据 */}
      <FAQStructuredData faqs={imageFoxFAQs} />
    </>
  )
}
