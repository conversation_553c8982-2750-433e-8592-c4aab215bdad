'use client'

import { useState } from 'react'
import { Turnstile } from '@marsidev/react-turnstile'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'

export default function MessageForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [token, setToken] = useState<string>('')

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!token) {
      toast.error('请完成人机验证')
      return
    }

    setIsSubmitting(true)
    const formData = new FormData(e.currentTarget)
    
    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.get('name'),
          email: formData.get('email'),
          content: formData.get('content'),
          token,
        }),
      })

      if (!response.ok) {
        throw new Error('提交失败')
      }

      toast.success('留言提交成功')
      e.currentTarget.reset()
      setToken('')
    } catch (error) {
      toast.error('提交失败，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-lg mx-auto">
      <div>
        <Input
          name="name"
          placeholder="您的姓名"
          required
          className="w-full"
        />
      </div>
      <div>
        <Input
          name="email"
          type="email"
          placeholder="您的邮箱"
          required
          className="w-full"
        />
      </div>
      <div>
        <Textarea
          name="content"
          placeholder="请输入您的留言内容"
          required
          className="w-full min-h-[150px]"
        />
      </div>
      <div className="flex justify-center">
        <Turnstile
          siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY!}
          onSuccess={setToken}
        />
      </div>
      <Button
        type="submit"
        className="w-full"
        disabled={isSubmitting}
      >
        {isSubmitting ? '提交中...' : '提交留言'}
      </Button>
    </form>
  )
} 